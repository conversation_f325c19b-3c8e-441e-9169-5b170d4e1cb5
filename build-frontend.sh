#!/bin/bash
# Frontend-only build script for Cloudflare Pages
# This script explicitly avoids any Python/Poetry dependencies

set -e  # Exit on any error

echo "🚀 Starting Emma Studio frontend build..."
echo "📍 Current directory: $(pwd)"

# Explicitly disable Python/Poetry
export DISABLE_POETRY=true
export DISABLE_PYTHON=true
export PYTHON_VERSION=""
export NODE_VERSION=18

echo "🚫 Python/Poetry detection disabled"
echo "✅ Node.js version: $NODE_VERSION"
echo "📂 Directory contents:"
ls -la

# Ensure we're in the right directory
if [ ! -d "client" ]; then
    echo "❌ Error: client directory not found!"
    exit 1
fi

# Navigate to client directory
echo "📁 Navigating to client directory..."
cd client || exit 1

echo "📍 Now in client directory: $(pwd)"
echo "📂 Client directory contents:"
ls -la

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found in client directory!"
    exit 1
fi

echo "📦 Installing dependencies..."
npm ci --only=production=false

echo "🔨 Building frontend with Vite..."
npm run build

echo "✅ Frontend build completed!"
echo "📁 Build output directory:"
ls -la dist/

echo "🎉 Emma Studio frontend ready for deployment!"
