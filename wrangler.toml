# Wrangler configuration for Emma Studio frontend deployment
name = "emma-studio"
compatibility_date = "2024-01-01"

# Pages configuration
[env.production]
compatibility_date = "2024-01-01"

# Build configuration - frontend only
[[env.production.build]]
command = "cd client && npm install && npm run build"
cwd = "."
watch_dir = "client"

# Disable Python/Poetry detection
[build]
command = "cd client && npm install && npm run build"
publish = "client/dist"

# Environment variables
[vars]
NODE_ENV = "production"
