{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"build": "cd client && npm install && npm run build", "dev": "cd client && npm run dev", "preview": "cd client && npm run preview", "install-client": "cd client && npm install", "build:client": "cd client && npm install && npm run build", "dev:client": "cd client && npm run dev", "start:client": "cd client && npm run preview", "format:client": "npx prettier --write \"client/src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "lint:client": "npx eslint \"client/src/**/*.{ts,tsx}\" --fix", "format": "npm run format:client", "lint": "npm run lint:client", "check": "cd client && npx tsc --noEmit"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@blueprintjs/core": "^5.18.0", "@blueprintjs/popover2": "^2.1.30", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@firebase/app": "^0.11.4", "@firebase/auth": "^1.10.0", "@firebase/firestore": "^4.7.10", "@firebase/storage": "^0.13.7", "@google/generative-ai": "^0.24.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@langchain/core": "^0.3.43", "@langchain/google-genai": "^0.1.2", "@langchain/openai": "^0.5.10", "@neondatabase/serverless": "^0.10.4", "@phosphor-icons/react": "^2.1.7", "@qdrant/js-client-rest": "^1.14.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@tanstack/react-query": "^5.60.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/fabric": "^5.3.10", "@types/form-data": "^2.2.1", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/pg": "^8.11.13", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "color-namer": "^1.4.0", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^3.6.0", "dom-to-image": "^2.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-session": "^1.18.1", "fabric": "^6.6.4", "firebase": "^11.6.0", "form-data": "^4.0.2", "framer-motion": "^11.13.1", "gsap": "^3.12.7", "heic-convert": "^2.1.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "konva": "^8.4.3", "langchain": "^0.3.20", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "mime-types": "^3.0.1", "mobx-react-lite": "^4.1.0", "multer": "^1.4.5-lts.2", "node-fetch": "^3.3.2", "openai": "^4.95.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.14.1", "polotno": "^2.21.11", "quill": "^2.0.3", "react": "^18.3.1", "react-color": "^2.19.3", "react-color-palette": "^7.3.0", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.1", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.53.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-image-crop": "^11.0.7", "react-konva": "^18.2.10", "react-konva-utils": "^1.1.0", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.4", "recharts": "^2.13.0", "sharp": "^0.34.1", "slate": "^0.112.0", "slate-react": "^0.112.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "use-image": "^1.1.1", "uuid": "^11.1.0", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.1", "@openapitools/openapi-generator-cli": "^2.19.1", "@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "concurrently": "^7.6.0", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.2", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "openapi-typescript-codegen": "^0.29.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "5.6.3", "typescript-eslint": "^8.31.1", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}