# Ignore backend directory for Cloudflare Pages frontend deployment
backend/
src/
agents/
crewai_app/

# Python files and dependencies
*.py
pyproject.toml
poetry.lock
requirements.txt
Pipfile
Pipfile.lock
setup.py
setup.cfg
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.coverage
.tox/
venv/
env/
.env.local
.env.production

# Documentation and config files
.git/
.gitignore
README.md
docs/
*.md
.gitmodules
.dockerignore
Dockerfile
docker-compose.yml

# IDE and OS files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Build artifacts
dist/
build/
*.egg-info/
.eggs/

# Logs
*.log
logs/
