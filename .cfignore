# AGGRESSIVE IGNORE FOR CLOUDFLARE PAGES - FRONTEND ONLY
# Ignore ALL backend and Python-related files

# Backend directories
backend/
src/
agents/
crewai_app/
server/
api/

# Python files and dependencies - COMPLETE BLOCK
*.py
*.pyx
*.pxd
*.pyi
pyproject.toml
poetry.lock
requirements.txt
requirements-dev.txt
requirements-prod.txt
Pipfile
Pipfile.lock
setup.py
setup.cfg
tox.ini
pytest.ini
.python-version
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.coverage
.tox/
venv/
env/
.venv/
.env.local

# Python virtual environments
bin/
lib/
lib64/
include/
share/
pyvenv.cfg

# Documentation and config files
.git/
.gitignore
README.md
docs/
*.md
.gitmodules
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.*.yml

# IDE and OS files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Build artifacts (except client/dist)
dist/
build/
*.egg-info/
.eggs/

# Logs
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/
